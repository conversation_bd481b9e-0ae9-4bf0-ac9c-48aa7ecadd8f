-- 商家删除功能修复验证SQL脚本
-- 使用前请先创建测试数据，然后执行删除操作，最后运行此脚本验证

-- 1. 创建测试数据（请在应用程序中执行，或手动插入）
/*
-- 插入测试商家
INSERT INTO shangjia (username, password, shangjia_name, shangjia_phone, shangjia_email, shangjia_address, shangjia_xingji_types, shangjia_yesno_types, new_money, shangjia_content, shangjia_delete, create_time) 
VALUES ('test_merchant', '123456', '测试商家', '13800138000', '<EMAIL>', '测试地址', 1, 2, 1000.0, '测试商家简介', 1, NOW());

-- 获取商家ID（假设为1）
SET @shangjia_id = 1;

-- 插入测试商品
INSERT INTO shangpin (shangjia_id, shangpin_name, shangpin_photo, shangpin_types, shangpin_kucun_number, shangpin_old_money, shangpin_new_money, shangpin_clicknum, shangpin_content, shangxia_types, shangpin_delete, create_time)
VALUES (@shangjia_id, '测试商品', 'test.jpg', 1, 100, 100.0, 80.0, 0, '测试商品介绍', 1, 1, NOW());

-- 获取商品ID（假设为1）
SET @shangpin_id = 1;

-- 插入测试用户
INSERT INTO yonghu (username, password, yonghu_name, yonghu_phone, yonghu_id_number, yonghu_email, sex_types, new_money, create_time)
VALUES ('test_user', '123456', '测试用户', '13900139000', '123456789012345678', '<EMAIL>', 1, 500.0, NOW());

-- 获取用户ID（假设为1）
SET @yonghu_id = 1;

-- 插入测试购物车
INSERT INTO cart (yonghu_id, shangpin_id, buy_number, create_time, insert_time)
VALUES (@yonghu_id, @shangpin_id, 2, NOW(), NOW());

-- 插入测试收藏
INSERT INTO shangpin_collection (shangpin_id, yonghu_id, shangpin_collection_types, insert_time, create_time)
VALUES (@shangpin_id, @yonghu_id, 1, NOW(), NOW());

-- 插入测试评价
INSERT INTO shangpin_commentback (shangpin_id, yonghu_id, shangpin_commentback_text, insert_time, create_time)
VALUES (@shangpin_id, @yonghu_id, '测试评价内容', NOW(), NOW());

-- 插入测试订单（如果有address表的话）
-- INSERT INTO shangpin_order (shangpin_order_uuid_number, address_id, shangpin_id, yonghu_id, buy_number, shangpin_order_true_price, shangpin_order_types, shangpin_order_payment_types, insert_time, create_time)
-- VALUES ('TEST001', 1, @shangpin_id, @yonghu_id, 1, 80.0, 1, 1, NOW(), NOW());
*/

-- 2. 删除前数据检查
SELECT '=== 删除前数据检查 ===' AS info;

-- 检查商家数据
SELECT '商家数据:' AS info;
SELECT id, username, shangjia_name, shangjia_delete, create_time 
FROM shangjia 
WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%';

-- 检查商品数据
SELECT '商品数据:' AS info;
SELECT id, shangjia_id, shangpin_name, shangpin_delete, create_time 
FROM shangpin 
WHERE shangpin_name LIKE '%测试%' OR shangjia_id IN (
    SELECT id FROM shangjia WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%'
);

-- 检查购物车数据
SELECT '购物车数据:' AS info;
SELECT COUNT(*) as cart_count
FROM cart 
WHERE shangpin_id IN (
    SELECT id FROM shangpin WHERE shangpin_name LIKE '%测试%' OR shangjia_id IN (
        SELECT id FROM shangjia WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%'
    )
);

-- 检查收藏数据
SELECT '收藏数据:' AS info;
SELECT COUNT(*) as collection_count
FROM shangpin_collection 
WHERE shangpin_id IN (
    SELECT id FROM shangpin WHERE shangpin_name LIKE '%测试%' OR shangjia_id IN (
        SELECT id FROM shangjia WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%'
    )
);

-- 检查评价数据
SELECT '评价数据:' AS info;
SELECT COUNT(*) as comment_count
FROM shangpin_commentback 
WHERE shangpin_id IN (
    SELECT id FROM shangpin WHERE shangpin_name LIKE '%测试%' OR shangjia_id IN (
        SELECT id FROM shangjia WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%'
    )
);

-- 检查订单数据
SELECT '订单数据:' AS info;
SELECT COUNT(*) as order_count
FROM shangpin_order 
WHERE shangpin_id IN (
    SELECT id FROM shangpin WHERE shangpin_name LIKE '%测试%' OR shangjia_id IN (
        SELECT id FROM shangjia WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%'
    )
);

-- 3. 执行删除操作后的验证
-- （请在应用程序中执行删除操作后运行以下查询）

SELECT '=== 删除后数据验证 ===' AS info;

-- 验证商家被逻辑删除
SELECT '商家删除验证:' AS info;
SELECT id, username, shangjia_name, shangjia_delete, 
       CASE 
           WHEN shangjia_delete = 2 THEN '✓ 已逻辑删除' 
           WHEN shangjia_delete = 1 THEN '✗ 未删除' 
           ELSE '? 状态异常' 
       END as delete_status
FROM shangjia 
WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%';

-- 验证商品被逻辑删除
SELECT '商品删除验证:' AS info;
SELECT id, shangjia_id, shangpin_name, shangpin_delete,
       CASE 
           WHEN shangpin_delete = 2 THEN '✓ 已逻辑删除' 
           WHEN shangpin_delete = 1 THEN '✗ 未删除' 
           ELSE '? 状态异常' 
       END as delete_status
FROM shangpin 
WHERE shangpin_name LIKE '%测试%' OR shangjia_id IN (
    SELECT id FROM shangjia WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%'
);

-- 验证购物车数据被物理删除
SELECT '购物车删除验证:' AS info;
SELECT COUNT(*) as remaining_cart_count,
       CASE 
           WHEN COUNT(*) = 0 THEN '✓ 购物车数据已清理' 
           ELSE '✗ 购物车数据未清理' 
       END as cleanup_status
FROM cart 
WHERE shangpin_id IN (
    SELECT id FROM shangpin WHERE shangpin_name LIKE '%测试%' OR shangjia_id IN (
        SELECT id FROM shangjia WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%'
    )
);

-- 验证收藏数据被物理删除
SELECT '收藏删除验证:' AS info;
SELECT COUNT(*) as remaining_collection_count,
       CASE 
           WHEN COUNT(*) = 0 THEN '✓ 收藏数据已清理' 
           ELSE '✗ 收藏数据未清理' 
       END as cleanup_status
FROM shangpin_collection 
WHERE shangpin_id IN (
    SELECT id FROM shangpin WHERE shangpin_name LIKE '%测试%' OR shangjia_id IN (
        SELECT id FROM shangjia WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%'
    )
);

-- 验证评价数据被物理删除
SELECT '评价删除验证:' AS info;
SELECT COUNT(*) as remaining_comment_count,
       CASE 
           WHEN COUNT(*) = 0 THEN '✓ 评价数据已清理' 
           ELSE '✗ 评价数据未清理' 
       END as cleanup_status
FROM shangpin_commentback 
WHERE shangpin_id IN (
    SELECT id FROM shangpin WHERE shangpin_name LIKE '%测试%' OR shangjia_id IN (
        SELECT id FROM shangjia WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%'
    )
);

-- 验证订单数据被物理删除
SELECT '订单删除验证:' AS info;
SELECT COUNT(*) as remaining_order_count,
       CASE 
           WHEN COUNT(*) = 0 THEN '✓ 订单数据已清理' 
           ELSE '✗ 订单数据未清理' 
       END as cleanup_status
FROM shangpin_order 
WHERE shangpin_id IN (
    SELECT id FROM shangpin WHERE shangpin_name LIKE '%测试%' OR shangjia_id IN (
        SELECT id FROM shangjia WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%'
    )
);

-- 4. 总结验证结果
SELECT '=== 修复验证总结 ===' AS info;
SELECT 
    '如果以上所有验证都显示 ✓，则说明商家删除功能修复成功！' as summary,
    '商家和商品应该被逻辑删除，相关数据应该被物理删除。' as note;

-- 5. 清理测试数据（可选）
/*
-- 如果需要清理测试数据，可以执行以下语句
DELETE FROM shangjia WHERE username = 'test_merchant' OR shangjia_name LIKE '%测试%';
DELETE FROM shangpin WHERE shangpin_name LIKE '%测试%';
DELETE FROM yonghu WHERE username = 'test_user' OR yonghu_name LIKE '%测试%';
*/
