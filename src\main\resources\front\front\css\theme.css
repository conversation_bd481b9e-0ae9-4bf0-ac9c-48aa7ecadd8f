/*定义全局css*/
body {

    /* 绿 */
    /* 2 全局公共主颜色 */
    --publicMainColor: #008761;
    /* 2 全局公共副颜色 */
    --publicSubColor:  #3CB371;

}

/*开始==================================导航栏样式3=========================================开始*/
#iframe {
    width: 100%;
    margin-top: 87px;
    padding-top: 50px;
}
#header {
	height: auto;
	background: #fff;
	border-bottom: 0;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
}
#header .nav-top {
	display: flex;
	align-items: center;
	padding: 0 20px;
	font-size: 16px;
	color: #2a8a15;
	box-sizing: border-box;
	height: 38px;
	background-color: var(--publicSubColor);
	box-shadow: 0 1px 6px rgba(0,0,0,.3);
	justify-content: center;
	position: relative;
}

#header .nav-top-img {
	width: 124px;
	height: 40px;
	padding: 0;
	margin: 0;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(0,0,0,.3);
	box-shadow: 0 0 6px rgba(0,0,0,.3);
}

#header .nav-top-title {
	line-height: 40px;
	font-size: 25px;
	color: rgba(255, 255, 255, 1);
	padding: 0 10px;
	margin: 0 10px;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(127, 78, 22, 1);
	box-shadow: 0 0 0px rgba(0,0,0,.3);
}

#header .nav-top-tel {
	line-height: 40px;
	font-size: 25px;
	color: rgba(222, 222, 222, 1);
	padding: 0 10px;
	margin: 0;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(179, 172, 172, 1);
	box-shadow: 0 0 0px rgba(0,0,0,.3);
}

#header .navs {
	display: flex;
	padding: 0 20px;
	align-items: center;
	box-sizing: border-box;
	height: 100px;
	background-color: var(--publicMainColor);
	box-shadow: 0 1px 6px rgba(0,0,0,0);
	justify-content: center;
}
#header .navs .title {
	width: auto;
	line-height: 40px;
	font-size: 16px;
	color: #333;
	padding: 0 10px;
	margin: 0 5px;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(0,0,0,.3);
	box-shadow: 0 0 6px rgba(0,0,0,0);
}
#header .navs li {
	display: inline-block;
	width: auto;
	line-height: 34px;
	padding: 0 10px;
	margin: 0 5px;
	color: rgba(255, 255, 255, 1);
	font-size: 20px;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(0,0,0,.3);
	background-color: $template2.front.base.nav.list.item.backgroundColor;
	box-shadow: 0 0 6px rgba(0,0,0,.1);
	text-align: center;
}
#header .navs li a{
	color: inherit;
}
#header .navs li.current a{
	color: inherit;
}
#header .navs li a:hover{
	color: inherit;
}
#header .navs li.current {
	color: #fff;
	font-size: 21px;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: $template2.front.base.nav.list.activeBox.borderColor;
	background-color: var(--publicSubColor);
	box-shadow: 0 0 0px rgba(255,0,0,.8);
}
#header .navs li:hover {
	color: #fff;
	font-size: 20px;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(0,0,0,.3);
	background-color: var(--publicSubColor);
	box-shadow: 0 0 0px rgba(0,0,0,.3);
}
/*结束==================================导航栏样式3=========================================结束*/

/*home页面数据样式 开始*/
/*home页面数据样式 结束*/

/*list页面数据样式 开始*/
	/*list页面数据样式 普通数据样式 开始*/
.gallery-demo {
	box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.07), 0 5px 8px 0 rgba(0, 0, 0, 0.05), 0 1px 14px 0 rgba(0, 0, 0, 0.04);
	transition: 0.5s all;
	-webkit-transition: 0.5s all;
	-moz-transition: 0.5s all;
	-o-transition: 0.5s all;
	-ms-transition: 0.5s all;
	margin-top: 40px;
}

.gallery-demo:hover {
	box-shadow: 0 12px 16px 0 rgba(0, 0, 0, .24), 0 17px 50px 0 rgba(0, 0, 0, .19);
	transition: 0.5s all;
	-webkit-transition: 0.5s all;
	-moz-transition: 0.5s all;
	-o-transition: 0.5s all;
	-ms-transition: 0.5s all;
}

h4.p-mask {
	color: #636363;
	font-size: 20px;
	letter-spacing: 1px;
	text-align: center;
	padding: 14px 10px 20px;
}

h4.p-mask span {
	color: #191919;
	font-size: 24px
}
	/*list页面数据样式 普通数据样式 结束*/
/*list页面数据样式 结束*/


/* 主页 轮播图选择框颜色 主*/
#test1 .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 6px var(--publicMainColor, #808080);
}
/* 个人中心轮播图 */
#swiper .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 6px var(--publicMainColor, #808080);
}

/* 大部分颜色 主 */
.main_color {
	color: var(--publicMainColor, #808080);
}
/* 边框颜色 主 */
.main_borderColor{
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 6px var(--publicMainColor, #808080);
}
/* 背景颜色 主 */
.main_backgroundColor {
	background-color: var(--publicMainColor, #808080);
}
/* 登录页面单选按钮颜色 主 */
.l-redio .layui-form-radioed>i {
	font-size: 16px;
	color: var(--publicMainColor, #808080);
}
.l-redio .layui-form-radioed>div {
	font-size: 14px;
	color: var(--publicMainColor, #808080);
}

/* 大部分颜色 副 */
.sub_color {
	color: var(--publicSubColor, #808080);
}
/* 边框颜色 副 */
.sub_borderColor{
	border-color: var(--publicSubColor, #808080);
	box-shadow: 0 0 6px var(--publicSubColor, #808080);
}
/* 背景颜色 副 */
.sub_backgroundColor {
	background-color: var(--publicSubColor, #808080);
}

/* 分页颜色 */
.layui-laypage .layui-laypage-curr .layui-laypage-em {
	background-color: var(--publicMainColor, #808080);
}

/* 评论和简介背景颜色 */
.detail-tab .layui-tab-card>.layui-tab-title .layui-this {
	background-color: var(--publicMainColor, #808080);
	color: #fff;
	font-size: 14px;
}
#swiper .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
}

/* 个人中心 菜单点击颜色*/
.center-container .layui-nav-tree .layui-nav-item.layui-this {
	background-color: var(--publicSubColor, #808080);
}
/*个人中心 菜单鼠标移上颜色*/
.center-container .layui-nav-tree .layui-nav-item:hover {
	background-color:var(--publicMainColor, #808080);
}
/*个人中心 菜单下线颜色*/
.center-container .layui-nav-tree .layui-nav-item {
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 输入框中字体颜色和边框颜色*/
.right-container .input .layui-input {
	color: var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 下拉框中字体颜色和边框颜色*/
.right-container .select .layui-input {
	color: var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 未知颜色*/
.right-container .date .layui-input {
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}

/* 前台elementUI得下拉框内容颜色和边框颜色修改 */
/* start */
.el-select-dropdown__item.selected {
	color: var(--publicMainColor, #808080);
	font-weight: bold;
}
.el-select .el-input.is-focus .el-input__inner {
	border-color: var(--publicMainColor, #808080);
}
.el-input--suffix .el-input__inner{
	color:var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
}
.el-select .el-input__inner:focus {
	border-color: var(--publicMainColor, #808080);
}
/* end */
/*=====================富文本框字体样式===========================================================================*/

.ql-size-small {
	font-size: 10px;
}
.ql-size-large {
	font-size: 18px;
}
.ql-size-huge {
	font-size: 32px;
}