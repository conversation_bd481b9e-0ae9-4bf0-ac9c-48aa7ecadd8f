
package com.controller;

import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.annotation.IgnoreAuth;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.entity.CartEntity;
import com.entity.ShangjiaEntity;
import com.entity.ShangpinCollectionEntity;
import com.entity.ShangpinCommentbackEntity;
import com.entity.ShangpinEntity;
import com.entity.ShangpinOrderEntity;
import com.entity.view.ShangjiaView;
import com.service.CartService;
import com.service.DictionaryService;
import com.service.ShangjiaService;
import com.service.ShangpinCollectionService;
import com.service.ShangpinCommentbackService;
import com.service.ShangpinOrderService;
import com.service.ShangpinService;
import com.service.TokenService;
import com.service.YonghuService;
import com.utils.PageUtils;
import com.utils.PoiUtil;
import com.utils.R;
import com.utils.StringUtil;

/**
 * 商家
 * 后端接口
 * <AUTHOR> @email
*/
@RestController
@Controller
@RequestMapping("/shangjia")
public class ShangjiaController {
    private static final Logger logger = LoggerFactory.getLogger(ShangjiaController.class);

    @Autowired
    private ShangjiaService shangjiaService;


    @Autowired
    private TokenService tokenService;
    @Autowired
    private DictionaryService dictionaryService;

    //级联表service
    @Autowired
    private ShangpinService shangpinService;
    @Autowired
    private ShangpinOrderService shangpinOrderService;
    @Autowired
    private CartService cartService;
    @Autowired
    private ShangpinCollectionService shangpinCollectionService;
    @Autowired
    private ShangpinCommentbackService shangpinCommentbackService;

    @Autowired
    private YonghuService yonghuService;


    /**
    * 后端列表
    */
    @RequestMapping("/page")
    public R page(@RequestParam Map<String, Object> params, HttpServletRequest request){
        logger.debug("page方法:,,Controller:{},,params:{}",this.getClass().getName(),JSONObject.toJSONString(params));
        String role = String.valueOf(request.getSession().getAttribute("role"));
        if(false)
            return R.error(511,"永不会进入");
        else if("用户".equals(role))
            params.put("yonghuId",request.getSession().getAttribute("userId"));
        else if("商家".equals(role))
            params.put("shangjiaId",request.getSession().getAttribute("userId"));
        params.put("shangjiaDeleteStart",1);params.put("shangjiaDeleteEnd",1);
        if(params.get("orderBy")==null || params.get("orderBy")==""){
            params.put("orderBy","id");
        }
        PageUtils page = shangjiaService.queryPage(params);

        //字典表数据转换
        List<ShangjiaView> list =(List<ShangjiaView>)page.getList();
        for(ShangjiaView c:list){
            //修改对应字典表字段
            dictionaryService.dictionaryConvert(c, request);
        }
        return R.ok().put("data", page);
    }

    /**
    * 后端详情
    */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id, HttpServletRequest request){
        logger.debug("info方法:,,Controller:{},,id:{}",this.getClass().getName(),id);
        ShangjiaEntity shangjia = shangjiaService.selectById(id);
        if(shangjia !=null){
            //entity转view
            ShangjiaView view = new ShangjiaView();
            BeanUtils.copyProperties( shangjia , view );//把实体数据重构到view中

            //修改对应字典表字段
            dictionaryService.dictionaryConvert(view, request);
            return R.ok().put("data", view);
        }else {
            return R.error(511,"查不到数据");
        }

    }

    /**
    * 后端保存
    */
    @RequestMapping("/save")
    public R save(@RequestBody ShangjiaEntity shangjia, HttpServletRequest request){
        logger.debug("save方法:,,Controller:{},,shangjia:{}",this.getClass().getName(),shangjia.toString());

        String role = String.valueOf(request.getSession().getAttribute("role"));
        if(false)
            return R.error(511,"永远不会进入");

        Wrapper<ShangjiaEntity> queryWrapper = new EntityWrapper<ShangjiaEntity>()
            .eq("username", shangjia.getUsername())
            .or()
            .eq("shangjia_phone", shangjia.getShangjiaPhone())
            .andNew()
            .eq("shangjia_delete", 1)
            ;

        logger.info("sql语句:"+queryWrapper.getSqlSegment());
        ShangjiaEntity shangjiaEntity = shangjiaService.selectOne(queryWrapper);
        if(shangjiaEntity==null){
            shangjia.setShangjiaYesnoTypes(1);
            shangjia.setShangjiaDelete(1);
            shangjia.setCreateTime(new Date());
            shangjia.setPassword("123456");
            shangjiaService.insert(shangjia);
            return R.ok();
        }else {
            return R.error(511,"账户或者联系方式已经被使用");
        }
    }

    /**
    * 后端修改
    */
    @RequestMapping("/update")
    public R update(@RequestBody ShangjiaEntity shangjia, HttpServletRequest request){
        logger.debug("update方法:,,Controller:{},,shangjia:{}",this.getClass().getName(),shangjia.toString());

        String role = String.valueOf(request.getSession().getAttribute("role"));
//        if(false)
//            return R.error(511,"永远不会进入");
        //根据字段查询是否有相同数据
        Wrapper<ShangjiaEntity> queryWrapper = new EntityWrapper<ShangjiaEntity>()
            .notIn("id",shangjia.getId())
            .andNew()
            .eq("username", shangjia.getUsername())
            .or()
            .eq("shangjia_phone", shangjia.getShangjiaPhone())
            .andNew()
            .eq("shangjia_delete", 1)
            ;

        logger.info("sql语句:"+queryWrapper.getSqlSegment());
        ShangjiaEntity shangjiaEntity = shangjiaService.selectOne(queryWrapper);
        if("".equals(shangjia.getShangjiaPhoto()) || "null".equals(shangjia.getShangjiaPhoto())){
                shangjia.setShangjiaPhoto(null);
        }
        if(shangjiaEntity==null){
            shangjiaService.updateById(shangjia);//根据id更新
            return R.ok();
        }else {
            return R.error(511,"账户或者联系方式已经被使用");
        }
    }

    /**
    * 删除
    */
    @RequestMapping("/delete")
    public R delete(@RequestBody Integer[] ids){
        logger.debug("delete:,,Controller:{},,ids:{}",this.getClass().getName(),ids.toString());

        // 级联删除商家相关数据
        for(Integer shangjiaId : ids){
            // 1. 查询该商家的所有商品
            List<ShangpinEntity> shangpinList = shangpinService.selectList(
                new EntityWrapper<ShangpinEntity>()
                    .eq("shangjia_id", shangjiaId)
                    .eq("shangpin_delete", 1)
            );

            if(shangpinList != null && shangpinList.size() > 0){
                // 获取所有商品ID
                List<Integer> shangpinIds = new ArrayList<>();
                for(ShangpinEntity shangpin : shangpinList){
                    shangpinIds.add(shangpin.getId());
                }

                // 2. 删除相关的购物车数据
                cartService.delete(new EntityWrapper<CartEntity>()
                    .in("shangpin_id", shangpinIds)
                );

                // 3. 删除相关的商品收藏数据
                shangpinCollectionService.delete(new EntityWrapper<ShangpinCollectionEntity>()
                    .in("shangpin_id", shangpinIds)
                );

                // 4. 删除相关的商品评价数据
                shangpinCommentbackService.delete(new EntityWrapper<ShangpinCommentbackEntity>()
                    .in("shangpin_id", shangpinIds)
                );

                // 5. 删除相关的商品订单数据
                shangpinOrderService.delete(new EntityWrapper<ShangpinOrderEntity>()
                    .in("shangpin_id", shangpinIds)
                );

                // 6. 逻辑删除该商家的所有商品
                ArrayList<ShangpinEntity> shangpinDeleteList = new ArrayList<>();
                for(Integer shangpinId : shangpinIds){
                    ShangpinEntity shangpinEntity = new ShangpinEntity();
                    shangpinEntity.setId(shangpinId);
                    shangpinEntity.setShangpinDelete(2);
                    shangpinDeleteList.add(shangpinEntity);
                }
                shangpinService.updateBatchById(shangpinDeleteList);
            }
        }

        // 7. 最后删除商家（可选择逻辑删除或物理删除）

        // 选项1：逻辑删除（推荐，可恢复）
        ArrayList<ShangjiaEntity> list = new ArrayList<>();
        for(Integer id:ids){
            ShangjiaEntity shangjiaEntity = new ShangjiaEntity();
            shangjiaEntity.setId(id);
            shangjiaEntity.setShangjiaDelete(2);
            list.add(shangjiaEntity);
        }
        if(list != null && list.size() >0){
            shangjiaService.updateBatchById(list);
        }

        // 选项2：物理删除（不可恢复，如需要请取消注释下面的代码，并注释上面的逻辑删除代码）
        /*
        shangjiaService.deleteBatchIds(Arrays.asList(ids));
        */
        return R.ok();
    }


    /**
     * 批量上传
     */
    @RequestMapping("/batchInsert")
    public R save( String fileName){
        logger.debug("batchInsert方法:,,Controller:{},,fileName:{}",this.getClass().getName(),fileName);
        try {
            List<ShangjiaEntity> shangjiaList = new ArrayList<>();//上传的东西
            Map<String, List<String>> seachFields= new HashMap<>();//要查询的字段
            Date date = new Date();
            int lastIndexOf = fileName.lastIndexOf(".");
            if(lastIndexOf == -1){
                return R.error(511,"该文件没有后缀");
            }else{
                String suffix = fileName.substring(lastIndexOf);
                if(!".xls".equals(suffix)){
                    return R.error(511,"只支持后缀为xls的excel文件");
                }else{
                    URL resource = this.getClass().getClassLoader().getResource("static/upload/" + fileName);//获取文件路径
                    File file = new File(resource.getFile());
                    if(!file.exists()){
                        return R.error(511,"找不到上传文件，请联系管理员");
                    }else{
                        List<List<String>> dataList = PoiUtil.poiImport(file.getPath());//读取xls文件
                        dataList.remove(0);//删除第一行，因为第一行是提示
                        for(List<String> data:dataList){
                            //循环
                            ShangjiaEntity shangjiaEntity = new ShangjiaEntity();
//                            shangjiaEntity.setUsername(data.get(0));                    //账户 要改的
//                            //shangjiaEntity.setPassword("123456");//密码
//                            shangjiaEntity.setShangjiaName(data.get(0));                    //商家名称 要改的
//                            shangjiaEntity.setShangjiaPhone(data.get(0));                    //联系方式 要改的
//                            shangjiaEntity.setShangjiaEmail(data.get(0));                    //邮箱 要改的
//                            shangjiaEntity.setShangjiaPhoto("");//照片
//                            shangjiaEntity.setShangjiaAddress(data.get(0));                    //商店地址 要改的
//                            shangjiaEntity.setShangjiaXingjiTypes(Integer.valueOf(data.get(0)));   //商家信用类型 要改的
//                            shangjiaEntity.setShangjiaYesnoTypes(Integer.valueOf(data.get(0)));   //商家状态 要改的
//                            shangjiaEntity.setNewMoney(data.get(0));                    //现有余额 要改的
//                            shangjiaEntity.setShangjiaContent("");//照片
//                            shangjiaEntity.setShangjiaDelete(1);//逻辑删除字段
//                            shangjiaEntity.setCreateTime(date);//时间
                            shangjiaList.add(shangjiaEntity);


                            //把要查询是否重复的字段放入map中
                                //账户
                                if(seachFields.containsKey("username")){
                                    List<String> username = seachFields.get("username");
                                    username.add(data.get(0));//要改的
                                }else{
                                    List<String> username = new ArrayList<>();
                                    username.add(data.get(0));//要改的
                                    seachFields.put("username",username);
                                }
                                //联系方式
                                if(seachFields.containsKey("shangjiaPhone")){
                                    List<String> shangjiaPhone = seachFields.get("shangjiaPhone");
                                    shangjiaPhone.add(data.get(0));//要改的
                                }else{
                                    List<String> shangjiaPhone = new ArrayList<>();
                                    shangjiaPhone.add(data.get(0));//要改的
                                    seachFields.put("shangjiaPhone",shangjiaPhone);
                                }
                        }

                        //查询是否重复
                         //账户
                        List<ShangjiaEntity> shangjiaEntities_username = shangjiaService.selectList(new EntityWrapper<ShangjiaEntity>().in("username", seachFields.get("username")).eq("shangjia_delete", 1));
                        if(shangjiaEntities_username.size() >0 ){
                            ArrayList<String> repeatFields = new ArrayList<>();
                            for(ShangjiaEntity s:shangjiaEntities_username){
                                repeatFields.add(s.getUsername());
                            }
                            return R.error(511,"数据库的该表中的 [账户] 字段已经存在 存在数据为:"+repeatFields.toString());
                        }
                         //联系方式
                        List<ShangjiaEntity> shangjiaEntities_shangjiaPhone = shangjiaService.selectList(new EntityWrapper<ShangjiaEntity>().in("shangjia_phone", seachFields.get("shangjiaPhone")).eq("shangjia_delete", 1));
                        if(shangjiaEntities_shangjiaPhone.size() >0 ){
                            ArrayList<String> repeatFields = new ArrayList<>();
                            for(ShangjiaEntity s:shangjiaEntities_shangjiaPhone){
                                repeatFields.add(s.getShangjiaPhone());
                            }
                            return R.error(511,"数据库的该表中的 [联系方式] 字段已经存在 存在数据为:"+repeatFields.toString());
                        }
                        shangjiaService.insertBatch(shangjiaList);
                        return R.ok();
                    }
                }
            }
        }catch (Exception e){
            return R.error(511,"批量插入数据异常，请联系管理员");
        }
    }


    /**
    * 登录
    */
    @IgnoreAuth
    @RequestMapping(value = "/login")
    public R login(String username, String password, String captcha, HttpServletRequest request) {
        ShangjiaEntity shangjia = shangjiaService.selectOne(new EntityWrapper<ShangjiaEntity>().eq("username", username));
        if(shangjia==null || !shangjia.getPassword().equals(password))
            return R.error("账号或密码不正确");
        else if(shangjia.getShangjiaDelete() != 1)
            return R.error("账户已被删除");
        else if(shangjia.getShangjiaYesnoTypes() != 2){
            if(shangjia.getShangjiaYesnoTypes() == 1)
                return R.error("商铺正在审核中,请等待");
            if(shangjia.getShangjiaYesnoTypes() == 3)
                return R.error("商铺审核已被拒绝.");

        }
        //  // 获取监听器中的字典表
        // ServletContext servletContext = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        // Map<String, Map<Integer, String>> dictionaryMap= (Map<String, Map<Integer, String>>) servletContext.getAttribute("dictionaryMap");
        // Map<Integer, String> role_types = dictionaryMap.get("role_types");
        // role_types.get(.getRoleTypes());
        String token = tokenService.generateToken(shangjia.getId(),username, "shangjia", "商家");
        R r = R.ok();
        r.put("token", token);
        r.put("role","商家");
        r.put("username",shangjia.getShangjiaName());
        r.put("tableName","shangjia");
        r.put("userId",shangjia.getId());
        return r;
    }

    /**
    * 注册
    */
    @IgnoreAuth
    @PostMapping(value = "/register")
    public R register(@RequestBody ShangjiaEntity shangjia){
//    	ValidatorUtils.validateEntity(user);
        Wrapper<ShangjiaEntity> queryWrapper = new EntityWrapper<ShangjiaEntity>()
            .eq("username", shangjia.getUsername())
            .or()
            .eq("shangjia_phone", shangjia.getShangjiaPhone())
            .andNew()
            .eq("shangjia_delete", 1)
            ;
        ShangjiaEntity shangjiaEntity = shangjiaService.selectOne(queryWrapper);
        if(shangjiaEntity != null)
            return R.error("账户或者联系方式已经被使用");
        shangjia.setShangjiaXingjiTypes(1);
        shangjia.setShangjiaYesnoTypes(1);
        shangjia.setNewMoney(0.0);
        shangjia.setShangjiaDelete(1);
        shangjia.setCreateTime(new Date());
        shangjiaService.insert(shangjia);
        return R.ok();
    }

    /**
     * 重置密码
     */
    @GetMapping(value = "/resetPassword")
    public R resetPassword(Integer  id){
        ShangjiaEntity shangjia = new ShangjiaEntity();
        shangjia.setPassword("123456");
        shangjia.setId(id);
        shangjiaService.updateById(shangjia);
        return R.ok();
    }


    /**
     * 忘记密码
     */
    @IgnoreAuth
    @RequestMapping(value = "/resetPass")
    public R resetPass(String username, HttpServletRequest request) {
        ShangjiaEntity shangjia = shangjiaService.selectOne(new EntityWrapper<ShangjiaEntity>().eq("username", username));
        if(shangjia!=null){
            shangjia.setPassword("123456");
            boolean b = shangjiaService.updateById(shangjia);
            if(!b){
               return R.error();
            }
        }else{
           return R.error("账号不存在");
        }
        return R.ok();
    }


    /**
    * 获取用户的session用户信息
    */
    @RequestMapping("/session")
    public R getCurrShangjia(HttpServletRequest request){
        Integer id = (Integer)request.getSession().getAttribute("userId");
        ShangjiaEntity shangjia = shangjiaService.selectById(id);
        if(shangjia !=null){
            //entity转view
            ShangjiaView view = new ShangjiaView();
            BeanUtils.copyProperties( shangjia , view );//把实体数据重构到view中

            //修改对应字典表字段
            dictionaryService.dictionaryConvert(view, request);
            return R.ok().put("data", view);
        }else {
            return R.error(511,"查不到数据");
        }
    }


    /**
    * 退出
    */
    @GetMapping(value = "logout")
    public R logout(HttpServletRequest request) {
        request.getSession().invalidate();
        return R.ok("退出成功");
    }




    /**
    * 前端列表
    */
    @IgnoreAuth
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params, HttpServletRequest request){
        logger.debug("list方法:,,Controller:{},,params:{}",this.getClass().getName(),JSONObject.toJSONString(params));

        // 没有指定排序字段就默认id倒序
        if(StringUtil.isEmpty(String.valueOf(params.get("orderBy")))){
            params.put("orderBy","id");
        }
        PageUtils page = shangjiaService.queryPage(params);

        //字典表数据转换
        List<ShangjiaView> list =(List<ShangjiaView>)page.getList();
        for(ShangjiaView c:list)
            dictionaryService.dictionaryConvert(c, request); //修改对应字典表字段
        return R.ok().put("data", page);
    }

    /**
    * 前端详情
    */
    @RequestMapping("/detail/{id}")
    public R detail(@PathVariable("id") Long id, HttpServletRequest request){
        logger.debug("detail方法:,,Controller:{},,id:{}",this.getClass().getName(),id);
        ShangjiaEntity shangjia = shangjiaService.selectById(id);
            if(shangjia !=null){


                //entity转view
                ShangjiaView view = new ShangjiaView();
                BeanUtils.copyProperties( shangjia , view );//把实体数据重构到view中

                //修改对应字典表字段
                dictionaryService.dictionaryConvert(view, request);
                return R.ok().put("data", view);
            }else {
                return R.error(511,"查不到数据");
            }
    }


    /**
    * 前端保存
    */
    @RequestMapping("/add")
    public R add(@RequestBody ShangjiaEntity shangjia, HttpServletRequest request){
        logger.debug("add方法:,,Controller:{},,shangjia:{}",this.getClass().getName(),shangjia.toString());
        Wrapper<ShangjiaEntity> queryWrapper = new EntityWrapper<ShangjiaEntity>()
            .eq("username", shangjia.getUsername())
            .or()
            .eq("shangjia_phone", shangjia.getShangjiaPhone())
            .andNew()
            .eq("shangjia_delete", 1)
            ;
        logger.info("sql语句:"+queryWrapper.getSqlSegment());
        ShangjiaEntity shangjiaEntity = shangjiaService.selectOne(queryWrapper);
        if(shangjiaEntity==null){
            shangjia.setShangjiaYesnoTypes(1);
            shangjia.setShangjiaDelete(1);
            shangjia.setCreateTime(new Date());
        shangjia.setPassword("123456");
        shangjiaService.insert(shangjia);
            return R.ok();
        }else {
            return R.error(511,"账户或者联系方式已经被使用");
        }
    }


}
